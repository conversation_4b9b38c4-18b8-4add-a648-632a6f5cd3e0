﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_communication/protocol/codec/airy_codec.h"
#include "mech_communication/protocol/codec/mech_base_codec.h"
#include "mech_communication/protocol/data_struct/airy.h"
#include "mech_communication/protocol/data_struct/helios.h"
#include "mech_communication/protocol/data_struct/ruby.h"
#include "mech_communication/register/register_airy.h"
#include "rsfsc_log/rsfsc_log_macro.h"

#include "rsfsc_log/rsfsc_log.h"
namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

AiryCodec::AiryCodec() = default;

std::string AiryCodec::getCurrentProtocolType() { return "Airy"; }

bool AiryCodec::packCtrlTxChannelExclusively(const int _channel_num,
                                             const bool _is_open,
                                             uint32_t& _expected_packet_response_code,
                                             std::vector<uint8_t>& _packet)
{
  if (_channel_num < 0 || static_cast<uint32_t>(_channel_num) > airy::CHANNEL_SIZE)
  {
    LOG_INDEX_ERROR("Invalid channel number: " + std::to_string(_channel_num));
    return false;
  }
  std::vector<uint32_t> reg_val_vec(airy::CHANNEL_SIZE / 8);
  for (uint32_t i = 0; i < airy::CHANNEL_SIZE / 8; ++i)
  {
    if (static_cast<uint32_t>((airy::CHANNEL_SIZE - 1 - static_cast<uint32_t>(_channel_num)) / 8) == i)
    {
      reg_val_vec[i] = _is_open ? 1U << (static_cast<uint32_t>(_channel_num) % 8) : 0x00;
    }
    else
    {
      reg_val_vec[i] = 0x00;
    }
  }

  return packWriteConTopRegData(airy::REG_TX_CHN_EN, reg_val_vec, _expected_packet_response_code, _packet);
}
bool AiryCodec::packReadTxChannelAll(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet)
{
  std::vector<uint32_t> reg_data(airy::CHANNEL_SIZE / 8);
  for (uint32_t i = 0; i < airy::CHANNEL_SIZE / 8; ++i)
  {
    reg_data.emplace_back(airy::REG_TX_CHN_EN + i);
  }
  return packReadTopRegister(reg_data, _expected_packet_response_code, _packet);
}
bool AiryCodec::packCtrlTxChannelAll(const bool _open,
                                     uint32_t& _expected_packet_response_code,
                                     std::vector<uint8_t>& _packet)
{
  return packWriteTopRegister({ { airy::REG_TX_CHN_EN_ALL, _open ? 1U : 0 } }, _expected_packet_response_code, _packet);
}
bool AiryCodec::packCtrlTxChannel(const int _channel_num,
                                  const bool _open,
                                  const uint32_t _curr_value,
                                  uint32_t& _expected_packet_response_code,
                                  std::vector<uint8_t>& _packet)
{
  uint32_t addr = 0;
  if (!getTxChannelRegAddr(_channel_num, addr))
  {
    return false;
  }
  uint32_t value = 1U << (static_cast<uint32_t>(_channel_num) % 8);
  value          = _open ? _curr_value | value : _curr_value & (~value);

  return packWriteConTopRegData(addr, { value }, _expected_packet_response_code, _packet);
}
bool AiryCodec::getTxChannelRegAddr(const int _channel_num, uint32_t& _tx_channel_reg_addr)
{
  if (_channel_num < 0 || static_cast<uint32_t>(_channel_num) > airy::CHANNEL_SIZE)
  {
    LOG_INDEX_ERROR("Invalid channel number: " + std::to_string(_channel_num));
    return false;
  }
  _tx_channel_reg_addr = airy::REG_TX_CHN_EN + (airy::CHANNEL_SIZE - 1 - _channel_num) / 8;
  return true;
}
bool AiryCodec::packWriteDigitalRegister(const RegisterData _register_data,
                                         uint32_t& _expected_packet_response_code,
                                         std::vector<uint8_t>& _packet)
{
  std::vector<uint32_t> reg_addr_vec  = { airy::REG_SENSOR_SPI_ADDR_0, airy::REG_SENSOR_SPI_ADDR_1,
                                         airy::REG_SENSOR_SPI_WDATA, airy::REG_SENSOR_SPI_CTRL,
                                         airy::REG_SENSOR_SPI_CTRL };
  std::vector<uint32_t> reg_value_vec = { (_register_data.address >> 8U) & 0xffU, _register_data.address & 0xffU,
                                          _register_data.value, 0, 1 };
  return packWriteTopRegister(reg_addr_vec, reg_value_vec, _expected_packet_response_code, _packet);
}

bool AiryCodec::isValidMsop(const char* _packet)
{
  const helios::MsopPacket* pkt_data = static_cast<const helios::MsopPacket*>(static_cast<const void*>(_packet));
  return pkt_data->frame_flag == ruby::MSOP_FRAME_FLAG;
}

}  // namespace lidar
}  // namespace robosense
