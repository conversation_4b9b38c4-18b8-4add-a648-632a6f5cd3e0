﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_communication/protocol/codec/mech_base_codec.h"
#include "mech_communication/protocol/data_struct/bpearl.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "mech_communication/protocol/data_struct/ruby.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <boost/endian/conversion.hpp>
#include <iomanip>
#include <sstream>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
namespace mech
{
using CheckSumType                 = uint16_t;
using CheckSumType32               = uint32_t;
constexpr uint32_t FRAME_WRAP_SIZE = sizeof(FrameHead) - sizeof(FrameHead::cmd_type) -
                                     sizeof(FrameHead::response_type) + sizeof(FRAME_TAIL_FLAG) + sizeof(CheckSumType);
namespace server
{
constexpr uint32_t FRAME_WRAP_SIZE =
  sizeof(FrameHead) - sizeof(FrameHead::cmd_type) + sizeof(FRAME_TAIL_FLAG) + sizeof(CheckSumType);
}  // namespace server
}  // namespace mech

std::vector<uint8_t> MechBaseCodec::packRequest(const uint32_t _cmd_type, const std::vector<uint8_t>& _payload)
{
  // pack frame head
  std::vector<uint8_t> frame_array(requestFrameHeadPack(_cmd_type, static_cast<uint32_t>(_payload.size())));

  // pack frame payload data
  frame_array.insert(frame_array.end(), _payload.begin(), _payload.end());

  // pack frame tail
  frameTailPack(frame_array);

  // vector move semantic
  return frame_array;
}
std::vector<uint8_t> MechBaseCodec::packResponse(const uint32_t _cmd_type,
                                                 const mech::ResponseType _response_type,
                                                 const std::vector<uint8_t>& _payload)
{
  // pack frame head
  std::vector<uint8_t> frame_array(
    responseFrameHeadPack(_cmd_type, _response_type, static_cast<uint32_t>(_payload.size())));

  // pack frame payload data
  frame_array.insert(frame_array.end(), _payload.begin(), _payload.end());

  // pack frame tail
  frameTailPack(frame_array);

  // vector move semantic
  return frame_array;
}

void MechBaseCodec::clear()
{
  std::queue<std::pair<uint32_t, std::vector<uint8_t>>>().swap(payload_queue);
  std::queue<uint32_t>().swap(register_value_queue);
  std::queue<uint32_t>().swap(register_addr_queue);
  std::queue<std::vector<uint8_t>>().swap(universal_queue);
}

std::string MechBaseCodec::hex(std::vector<uint8_t> _data, uint32_t _display_max_len)
{
  std::stringstream string_stream;
  string_stream << std::hex;

  auto size = _data.size() < _display_max_len ? _data.size() : _display_max_len;

  for (std::size_t i = 0; i < size; ++i)
  {
    string_stream << std::setw(2) << std::setfill('0') << static_cast<uint32_t>(_data.at(i)) << " ";
  }
  if (_data.size() > _display_max_len)
  {
    string_stream << "......";
  }
  return string_stream.str();
}

std::string MechBaseCodec::hex(uint32_t _data)
{
  std::stringstream string_stream;
  string_stream << std::hex << std::setfill('0') << _data;
  return string_stream.str();
}

std::vector<uint32_t> MechBaseCodec::handleParsedPayloads()
{
  std::lock_guard<std::mutex> lock(queue_mutex);
  std::vector<uint32_t> response_codes;
  while (!payload_queue.empty())
  {
    auto payload_pair = payload_queue.front();
    payload_queue.pop();

    auto response_code = payload_pair.first;
    auto payload       = payload_pair.second;
    response_codes.push_back(response_code);

    if (!extractData(response_code, payload))
    {
      error_str = "extract data failed, response_code: " + MechBaseCodec::hex(payload) + ", parser err: " + error_str;
    }
  }
  return response_codes;
}

uint16_t MechBaseCodec::checkSum(const uint8_t* _data, uint16_t _length)
{
  uint32_t check_sum = 0;
  while (_length != 0)
  {
    check_sum += _data[--_length];  // NOLINT
  }

  check_sum = check_sum & 0xffffU;

  return static_cast<uint16_t>(check_sum);
}

std::vector<uint8_t>::iterator MechBaseCodec::findFrameFlag(std::vector<uint8_t>& _buffer, uint32_t _frame_flag)
{
  for (auto iter = _buffer.begin(); iter <= _buffer.end() - sizeof(_frame_flag); ++iter)
  {
    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
    if (*reinterpret_cast<const uint32_t*>(&(*iter)) == _frame_flag)
    {
      return iter;
    }
  }
  return _buffer.end();
}

std::string MechBaseCodec::getCurrentProtocolType() { return "Mech Base"; }

bool MechBaseCodec::packReadConfigPara(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet)
{
  std::vector<uint8_t> data;
  return packReadCmd(mech::NET_CMD_CONFIG_READ_ALL, data, _expected_packet_response_code, _packet);
}

bool MechBaseCodec::packWriteConRegData(const uint32_t _start_reg_addr,
                                        const std::vector<uint32_t>& _reg_val,
                                        uint32_t& _expected_packet_response_code,
                                        std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_BOTTOM_BOARD_CON_WRITE_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_val.size());

  std::vector<uint8_t> payload(sizeof(_start_reg_addr) + sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(uint32_t));
  size_t index = 0;
  // 数据长度 = addr(4) + reg_num(2) + val * reg_num(4 * n)
  copyToPayload(payload, _start_reg_addr, index);
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_val.data(), REGISTER_COUNT, index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}
bool MechBaseCodec::packWriteConTopRegData(const uint32_t _start_reg_addr,
                                           const std::vector<uint32_t>& _reg_val,
                                           uint32_t& _expected_packet_response_code,
                                           std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_CON_WRITE_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_val.size());

  std::vector<uint8_t> payload(sizeof(_start_reg_addr) + sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(uint32_t));
  size_t index = 0;
  // 数据长度 = addr(4) + reg_num(2) + val * reg_num(4 * n)
  copyToPayload(payload, _start_reg_addr, index);
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_val.data(), REGISTER_COUNT, index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseCodec::packReadConRegData(const uint32_t _start_reg_addr,
                                       const uint32_t _reg_num,
                                       uint32_t& _expected_packet_response_code,
                                       std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_BOTTOM_BOARD_CON_READ_REGISTER;
  uint16_t count                 = static_cast<uint16_t>(_reg_num);
  std::vector<uint8_t> payload(sizeof(_start_reg_addr) + sizeof(count));

  // 数据长度 = addr(4byte) + reg_num(2byte)
  size_t index = 0;
  copyToPayload(payload, _start_reg_addr, index);
  copyToPayload(payload, count, index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}
bool MechBaseCodec::packReadConTopRegData(const uint32_t _start_reg_addr,
                                          const uint32_t _reg_num,
                                          uint32_t& _expected_packet_response_code,
                                          std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_CON_READ_REGISTER;
  uint16_t count                 = static_cast<uint16_t>(_reg_num);
  std::vector<uint8_t> payload(sizeof(_start_reg_addr) + sizeof(count));

  // 数据长度 = addr(4byte) + reg_num(2byte)
  size_t index = 0;
  copyToPayload(payload, _start_reg_addr, index);
  copyToPayload(payload, count, index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseCodec::packWriteDigitalRegister(const RegisterData /*_register_data*/,
                                             uint32_t& /*_expected_packet_response_code*/,
                                             std::vector<uint8_t>& /*_packet*/)
{
  error_str = "unimplemented function: MechBaseCodec::packTxChannelCtrlExclusively";
  return false;
}
bool MechBaseCodec::packReadDigitalRegister(const uint32_t /*_reg_addr*/,
                                            uint32_t& /*_expected_packet_response_code*/,
                                            std::vector<uint8_t>& /*_packet*/)
{
  error_str = "unimplemented function: MechBaseCodec::packTxChannelCtrlExclusively";
  return false;
}

bool MechBaseCodec::packReadRegister(const std::vector<uint32_t>& _reg_addr,
                                     uint32_t& _expected_packet_response_code,
                                     std::vector<uint8_t>& _packet)
{
  if (_reg_addr.empty())
  {
    LOG_INDEX_ERROR("addr or val number error, addr size: {}, val size: ", _reg_addr.size());

    return false;
  }
  _expected_packet_response_code = mech::NET_CMD_BOTTOM_BOARD_MULTI_READ_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_addr.size());

  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(uint32_t));
  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_addr.data(), _reg_addr.size(), index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseCodec::packWriteRegister(const std::vector<uint32_t>& _reg_addr,
                                      const std::vector<uint32_t>& _reg_val,
                                      uint32_t& _expected_packet_response_code,
                                      std::vector<uint8_t>& _packet)
{
  if ((_reg_addr.size() != _reg_val.size()) || _reg_addr.empty())
  {
    LOG_INDEX_ERROR("addr or val number error, addr size: {}, val size: " + std::to_string(_reg_val.size()),
                    _reg_addr.size());
    return false;
  }
  if (_reg_addr.size() == 1)
  {
    return packWriteConRegData(_reg_addr.at(0), _reg_val, _expected_packet_response_code, _packet);
  }

  _expected_packet_response_code = mech::NET_CMD_BOTTOM_BOARD_MULTI_WRITE_REGISTER;

  const uint16_t REGISTER_COUNT = static_cast<uint16_t>(_reg_addr.size());
  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(RegisterData));

  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  for (std::vector<uint32_t>::size_type i = 0; i < _reg_addr.size(); ++i)
  {
    copyToPayload(payload, _reg_addr.at(i), index);
    copyToPayload(payload, _reg_val.at(i), index);
  }

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseCodec::packWriteRegister(const std::vector<RegisterData>& _reg_data,
                                      uint32_t& _expected_packet_response_code,
                                      std::vector<uint8_t>& _packet)
{
  if (_reg_data.size() == 1)
  {
    std::vector<uint32_t> reg_val;
    reg_val.emplace_back(_reg_data.at(0).value);
    return packWriteConRegData(_reg_data.at(0).address, reg_val, _expected_packet_response_code, _packet);
  }

  _expected_packet_response_code = mech::NET_CMD_BOTTOM_BOARD_MULTI_WRITE_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_data.size());

  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(RegisterData));
  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_data.data(), REGISTER_COUNT, index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseCodec::packWriteTopRegister(const std::vector<uint32_t>& _reg_addr,
                                         const std::vector<uint32_t>& _reg_val,
                                         uint32_t& _expected_packet_response_code,
                                         std::vector<uint8_t>& _packet)
{
  if ((_reg_addr.size() != _reg_val.size()) || _reg_addr.empty())
  {
    LOG_INDEX_ERROR("addr or val number error, addr size: {}, val size: " + std::to_string(_reg_val.size()),
                    _reg_addr.size());
    return false;
  }

  if (_reg_addr.size() == 1)
  {
    return packWriteConTopRegData(_reg_addr.at(0), _reg_val, _expected_packet_response_code, _packet);
  }

  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_MULTI_WRITE_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_addr.size());

  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(RegisterData));
  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  for (std::vector<uint32_t>::size_type i = 0; i < _reg_addr.size(); ++i)
  {
    copyToPayload(payload, _reg_addr.at(i), index);
    copyToPayload(payload, _reg_val.at(i), index);
  }
  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseCodec::packWriteTopRegister(const std::vector<RegisterData>& _reg_data,
                                         uint32_t& _expected_packet_response_code,
                                         std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_MULTI_WRITE_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_data.size());

  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(RegisterData));
  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_data.data(), REGISTER_COUNT, index);
  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseCodec::packReadTopRegister(const std::vector<uint32_t>& _reg_addr,
                                        uint32_t& _expected_packet_response_code,
                                        std::vector<uint8_t>& _packet)
{
  if (_reg_addr.empty())
  {
    LOG_INDEX_ERROR("addr or val number error, addr size: {}, val size: ", _reg_addr.size());

    return false;
  }
  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_MULTI_READ_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_addr.size());

  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(uint32_t));
  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_addr.data(), _reg_addr.size(), index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseCodec::packGetEyesSafe(uint32_t& /*_expected_packet_response_code*/, std::vector<uint8_t>& /*_packet*/)
{
  error_str = "this protocol unsupported get eyes safe";
  return false;
}

bool MechBaseCodec::packSetEyesSafe(const uint32_t _is_open,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet)
{
  std::vector<uint8_t> payload(sizeof(uint16_t));
  uint16_t is_open = static_cast<uint16_t>(_is_open);
  std::memcpy(payload.data(), &is_open, sizeof(is_open));
  return packWriteCmd(mech::NET_CMD_TOP_BOARD_EYES_SAFE, payload, _expected_packet_response_code, _packet);
}

bool MechBaseCodec::packReadCmd(const uint32_t _cmd_type,
                                const std::vector<uint8_t>& _data,
                                uint32_t& _expected_packet_response_code,
                                std::vector<uint8_t>& _packet)
{
  if (_cmd_type > mech::NET_CMD_END || _cmd_type < mech::NET_CMD_BEGIN)
  {
    error_str = "cmd type error out of range, cmd type: " + std::to_string(_cmd_type);
    return false;
  }
  _expected_packet_response_code = _cmd_type;

  _packet = packRequest(_expected_packet_response_code, _data);
  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechBaseCodec::packWriteCmd(const uint32_t _cmd_type,
                                 const std::vector<uint8_t>& _data,
                                 uint32_t& _expected_packet_response_code,
                                 std::vector<uint8_t>& _packet)
{
  if (_cmd_type > mech::NET_CMD_END || _cmd_type < mech::NET_CMD_BEGIN)
  {
    return false;
  }
  _expected_packet_response_code = _cmd_type;

  _packet = packRequest(_expected_packet_response_code, _data);
  return true;
}

bool MechBaseCodec::packWriteIpPort(const uint32_t _ip,
                                    // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
                                    uint16_t _msop_port,
                                    uint16_t _difop_port,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet)
{
  if (_packet.size() != sizeof(mech::ConfigPara))
  {
    return false;
  }
  _expected_packet_response_code = mech::NET_CMD_CONFIG_SET_NETWORK;

  mech::ConfigPara config_para {};
  std::memcpy(&config_para, _packet.data(), sizeof(mech::ConfigPara));

  if (_msop_port == 0)
  {
    _msop_port = config_para.net_info.msop_port;
  }

  if (_difop_port == 0)
  {
    _difop_port = config_para.net_info.difop_port;
  }

  mech::NetPara net_para {};
  net_para = config_para.net_info;

  std::memcpy(&net_para.ip_local, &_ip, sizeof(_ip));
  net_para.msop_port  = _msop_port;
  net_para.difop_port = _difop_port;

  std::vector<uint8_t> payload(sizeof(net_para));

  auto index = 0;
  // pack net para
  std::memcpy(&payload.at(index), &net_para, sizeof(net_para));

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseCodec::packReadIntensity(const mech::IntensityData& _intensity_data,
                                      uint32_t& _expected_packet_response_code,
                                      std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_GET_INTENSITY;
  std::vector<uint8_t> payload(sizeof(mech::IntensityData));
  size_t index = 0;
  std::memcpy(&payload.at(index), &_intensity_data, sizeof(mech::IntensityData));

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseCodec::packCtrlTxChannelExclusively(const int /*_channel_num*/,
                                                 const bool /*_is_open*/,
                                                 uint32_t& /*_expected_packet_response_code*/,
                                                 std::vector<uint8_t>& /*_packet*/)
{
  LOG_INDEX_ERROR("MechBaseCodec:: unimplemented function: packTxChannelCtrlExclusively");
  return false;
}
bool MechBaseCodec::packReadTxChannelAll(uint32_t& /*_expected_packet_response_code*/,
                                         std::vector<uint8_t>& /*_packet*/)
{
  LOG_INDEX_ERROR("MechBaseCodec:: unimplemented function: packReadTxChannelAll");
  return false;
}
bool MechBaseCodec::packCtrlTxChannelAll(const bool _open,
                                         uint32_t& _expected_packet_response_code,
                                         std::vector<uint8_t>& _packet)
{
  LOG_INDEX_ERROR("MechBaseCodec:: unimplemented function: packCtrlTxChannelAll");
  return false;
}
bool MechBaseCodec::packCtrlTxChannel(const int /*_channel_num*/,
                                      const bool /*_open*/,
                                      const uint32_t /*_curr_value*/,
                                      uint32_t& /*_expected_packet_response_code*/,
                                      std::vector<uint8_t>& /*_packet*/)
{
  LOG_INDEX_ERROR("MechBaseCodec:: unimplemented function: packCtrlTxChannel");
  return false;
}
bool MechBaseCodec::getTxChannelRegAddr(const int /*_channel_num*/, uint32_t& /*_tx_channel_reg_addr*/)
{
  LOG_INDEX_ERROR("unsupported function: getTxChannelRegAddr");
  return false;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechBaseCodec::packStartWriteTopFlash(const uint32_t _start_addr,
                                           const uint32_t _len,
                                           ExpectedResp& _expected_resp,
                                           std::vector<uint8_t>& _packet)
{
  _expected_resp.cmd = mech::NET_CMD_TOP_BOARD_WRITE_FLASH;
  std::vector<uint8_t> payload(sizeof(mech::FLASH_CMD_REQ_WRITE) + sizeof(_start_addr) + sizeof(_len));
  size_t index = 0;
  copyToPayload(payload, mech::FLASH_CMD_REQ_WRITE, index);
  copyToPayload(payload, _start_addr, index);
  copyToPayload(payload, _len, index);

  _packet = packRequest(_expected_resp.cmd, payload);

  // expected response
  index = 0;
  _expected_resp.data.resize(sizeof(mech::FLASH_CMD_REQ_WRITE));
  copyToPayload(_expected_resp.data, mech::FLASH_CMD_REQ_WRITE, index);

  return true;
}
bool MechBaseCodec::packWriteTopFlash(const uint32_t _pkt_count,
                                      const std::vector<uint8_t>& _data,
                                      ExpectedResp& _expected_resp,
                                      std::vector<uint8_t>& _packet)
{
  if (_data.empty() || _data.size() > 1024)
  {
    LOG_INDEX_ERROR("MechBaseCodec::packWriteTopFlash data size out of range 1~1024, current data size: {}",
                    _data.size());
    return false;
  }

  _expected_resp.cmd = mech::NET_CMD_TOP_BOARD_WRITE_FLASH;
  std::vector<uint8_t> payload(sizeof(mech::FLASH_CMD_WRITE) + sizeof(_pkt_count) + _data.size());
  size_t index = 0;
  copyToPayload(payload, mech::FLASH_CMD_WRITE, index);
  copyToPayload(payload, _pkt_count, index);
  copyArrayToPayload(payload, _data.data(), _data.size(), index);

  _packet = packRequest(_expected_resp.cmd, payload);

  // expected response
  index = 0;
  _expected_resp.data.resize(sizeof(mech::FLASH_CMD_WRITE));
  copyToPayload(_expected_resp.data, mech::FLASH_CMD_WRITE, index);
  // copyToPayload(_expected_resp.data, _pkt_count, index);

  return true;
}
bool MechBaseCodec::packFinishWriteTopFlash(ExpectedResp& _expected_resp, std::vector<uint8_t>& _packet)
{
  _expected_resp.cmd = mech::NET_CMD_TOP_BOARD_WRITE_FLASH;
  std::vector<uint8_t> payload(sizeof(mech::FLASH_CMD_WRITE_FINISH));
  size_t index = 0;
  copyToPayload(payload, mech::FLASH_CMD_WRITE_FINISH, index);

  _packet = packRequest(_expected_resp.cmd, payload);

  // expected response
  index = 0;
  _expected_resp.data.resize(sizeof(mech::FLASH_CMD_WRITE_FINISH));
  copyToPayload(_expected_resp.data, mech::FLASH_CMD_WRITE_FINISH, index);

  return true;
}

bool MechBaseCodec::isValidMsop(const char* _packet)
{
  const mech::MsopPacket* pkt_data = static_cast<const mech::MsopPacket*>(static_cast<const void*>(_packet));

  return pkt_data->frame_flag == bpearl::MSOP_FRAME_FLAG;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
std::vector<uint8_t> MechBaseCodec::requestFrameHeadPack(const uint32_t _cmd_type, const uint32_t _payload_size)
{
  mech::FrameHead frame_head {};
  auto correct_payload_size = _payload_size + sizeof(mech::FrameHead::cmd_type);

  // pack frame head，length should add cmd size
  frame_head.frame_header = mech::FRAME_FLAG;
  frame_head.frame_type   = mech::FRAME_TYPE_REQUEST;
  frame_head.length       = boost::endian::native_to_big(static_cast<uint16_t>(correct_payload_size));
  frame_head.cmd_type     = boost::endian::native_to_big(static_cast<uint16_t>(_cmd_type));

  auto header_size = sizeof(mech::FrameHead) - sizeof(mech::FrameHead::response_type);
  std::vector<uint8_t> frame_array(header_size);

  // frame head size + payload size + frame tail size + check sum size
  frame_array.reserve(header_size + correct_payload_size + sizeof(mech::FRAME_TAIL_FLAG) + sizeof(mech::CheckSumType));

  std::memcpy(frame_array.data(), &frame_head, header_size);

  return frame_array;
}

std::vector<uint8_t> MechBaseCodec::responseFrameHeadPack(const uint32_t _cmd_type,
                                                          const mech::ResponseType _response_type,
                                                          const uint32_t _payload_size)
{
  mech::FrameHead frame_head {};
  auto correct_payload_size =
    _payload_size + sizeof(mech::FrameHead::cmd_type) + sizeof(mech::FrameHead::response_type);

  // pack frame head，length should add cmd size
  frame_head.frame_header  = mech::FRAME_FLAG;
  frame_head.frame_type    = mech::FRAME_TYPE_ACK;
  frame_head.length        = boost::endian::native_to_big(static_cast<uint16_t>(correct_payload_size));
  frame_head.cmd_type      = boost::endian::native_to_big(static_cast<uint16_t>(_cmd_type));
  frame_head.response_type = static_cast<uint8_t>(_response_type);

  auto header_size = sizeof(mech::FrameHead);
  std::vector<uint8_t> frame_array(header_size);

  // frame head size + payload size + frame tail size + check sum size
  frame_array.reserve(header_size + correct_payload_size + sizeof(mech::FRAME_TAIL_FLAG) + sizeof(mech::CheckSumType));

  std::memcpy(frame_array.data(), &frame_head, header_size);

  return frame_array;
}

std::vector<uint8_t> MechBaseCodec::frameTailPack(std::vector<uint8_t>& _frame_array)
{
  // notice that it is necessary to include the tail byte and frame type of
  // the data frame for calculating checksum
  _frame_array.emplace_back(mech::FRAME_TAIL_FLAG);

  // cppcoreguidelines-pro-bounds-pointer-arithmetic
  const uint16_t CHECK_SUM = checkSum(&_frame_array.at(sizeof(mech::FRAME_FLAG)),
                                      static_cast<uint16_t>(_frame_array.size() - sizeof(mech::FRAME_FLAG)));

  _frame_array.emplace_back((CHECK_SUM >> 8U) & 0xFFU);  // NOLINT
  _frame_array.emplace_back(CHECK_SUM & 0xFFU);

  return _frame_array;
}

bool MechBaseCodec::parseClientPacket(std::vector<uint8_t>& _packet_buffer)
{
  if (_packet_buffer.size() < sizeof(mech::FrameHead))
  {
    error_str = "payload size error, receive payload size:" + std::to_string(_packet_buffer.size());
    return false;
  }

  auto* frame_head = static_cast<mech::FrameHead*>(static_cast<void*>(_packet_buffer.data()));

  while (frame_head->frame_header == mech::FRAME_FLAG)
  {
    auto payload_size = boost::endian::big_to_native(frame_head->length);

    auto expected_data_size = payload_size + mech::FRAME_WRAP_SIZE;
    if (_packet_buffer.size() < expected_data_size)
    {
      error_str = "data length error, expect not less than: " + std::to_string(expected_data_size) +
                  ", actual: " + std::to_string(_packet_buffer.size());
      return false;
    }

    auto check_sum_data_size = expected_data_size - sizeof(mech::FRAME_FLAG) - sizeof(mech::CheckSumType);
    const mech::CheckSumType CAL_CHECK_SUM =
      checkSum(&_packet_buffer.at(sizeof(mech::FRAME_FLAG)), static_cast<uint16_t>(check_sum_data_size));

    mech::CheckSumType check_sum = 0;
    std::memcpy(&check_sum, &_packet_buffer.at(expected_data_size - sizeof(mech::CheckSumType)), sizeof(check_sum));
    check_sum = boost::endian::big_to_native(check_sum);

    if (CAL_CHECK_SUM != check_sum)
    {
      error_str = "check sum error, expect: " + hex(CAL_CHECK_SUM) + ", actual: " + hex(check_sum);
      _packet_buffer.clear();
      return false;
    }

    mech::ResponseType response_type = static_cast<mech::ResponseType>(frame_head->response_type);
    switch (response_type)
    {
    case mech::ResponseType::SUCCESS:
    {
      error_str = "";
      break;
    }
    case mech::ResponseType::UNSUPPORTED:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 不支持该指令";
      break;
    }
    case mech::ResponseType::PARAMETER_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 参数错误";
      break;
    }
    case mech::ResponseType::DATA_LEN_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 数据长度错误";
      break;
    }
    case mech::ResponseType::FORMAT_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 格式错误";
      break;
    }
    case mech::ResponseType::CHECKSUM_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 校验和错误";
      break;
    }
    case mech::ResponseType::OTHER:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 其他错误";
      break;
    }
    case mech::ResponseType::TIMEOUT:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 超时错误";
      break;
    }
    case mech::ResponseType::CURRENT_STATUS:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 当前状态错误";
      break;
    }
    case mech::ResponseType::VERSION_UNSUPPORTED:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 版本不支持";
      break;
    }
    default:
    {
      error_str = fmt::format("错误码: {}", static_cast<uint32_t>(response_type));
      break;
    }
    }
    const std::vector<uint8_t> PAYLOAD(
      _packet_buffer.begin() + sizeof(mech::FrameHead),
      _packet_buffer.begin() + expected_data_size - sizeof(mech::FRAME_TAIL_FLAG) - sizeof(mech::CheckSumType));
    _packet_buffer.erase(_packet_buffer.begin(), _packet_buffer.begin() + expected_data_size);

    if (response_type != mech::ResponseType::SUCCESS)
    {
      return false;
    }

    {
      std::lock_guard<std::mutex> lock(queue_mutex);
      payload_queue.emplace(boost::endian::big_to_native(frame_head->cmd_type), PAYLOAD);
    }

    if (_packet_buffer.size() < sizeof(mech::FrameHead))
    {
      break;
    }
    frame_head = static_cast<mech::FrameHead*>(static_cast<void*>(_packet_buffer.data()));
  }

  return true;
}

bool MechBaseCodec::parseServerPacket(std::vector<uint8_t>& _packet_buffer)
{
  while (true)
  {
    if (_packet_buffer.size() < sizeof(mech::server::FrameHead))
    {
      error_str = "packet too small to contain frame head, size: " + std::to_string(_packet_buffer.size());
      return false;
    }

    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
    auto* frame_head = reinterpret_cast<mech::server::FrameHead*>(_packet_buffer.data());

    // 协议头非法，说明收到的是脏数据，提示错误
    if (frame_head->frame_header != mech::FRAME_FLAG)
    {
      error_str = "invalid frame header: 0x" + hex(frame_head->frame_header);
      return false;
    }

    auto payload_size       = boost::endian::big_to_native(frame_head->length);
    auto expected_data_size = payload_size + mech::server::FRAME_WRAP_SIZE;

    if (_packet_buffer.size() < expected_data_size)
    {
      error_str = "incomplete frame, expected size: " + std::to_string(expected_data_size) +
                  ", actual: " + std::to_string(_packet_buffer.size());
      return false;
    }

    // 检查校验和
    auto check_sum_data_size = expected_data_size - sizeof(mech::FRAME_FLAG) - sizeof(mech::CheckSumType);
    const mech::CheckSumType CAL_CHECK_SUM =
      checkSum(&_packet_buffer.at(sizeof(mech::FRAME_FLAG)), static_cast<uint16_t>(check_sum_data_size));

    mech::CheckSumType check_sum = 0;
    std::memcpy(&check_sum, &_packet_buffer.at(expected_data_size - sizeof(mech::CheckSumType)), sizeof(check_sum));
    check_sum = boost::endian::big_to_native(check_sum);

    if (CAL_CHECK_SUM != check_sum)
    {
      error_str = "checksum mismatch, expected: " + hex(CAL_CHECK_SUM) + ", actual: " + hex(check_sum);
      _packet_buffer.clear();
      return false;
    }

    // 提取有效载荷
    const std::vector<uint8_t> PAYLOAD(
      _packet_buffer.begin() + sizeof(mech::server::FrameHead),
      _packet_buffer.begin() + expected_data_size - sizeof(mech::FRAME_TAIL_FLAG) - sizeof(mech::CheckSumType));

    {
      std::lock_guard<std::mutex> lock(queue_mutex);
      payload_queue.emplace(boost::endian::big_to_native(frame_head->cmd_type), PAYLOAD);
    }

    // 移除处理过的帧数据
    _packet_buffer.erase(_packet_buffer.begin(), _packet_buffer.begin() + expected_data_size);

    // 如果还有数据，尝试继续解析下一帧
    if (_packet_buffer.size() < sizeof(mech::server::FrameHead))
    {
      break;
    }
  }

  return true;
}

bool MechBaseCodec::extractRegister(const std::vector<uint8_t>& _payload)
{

  if ((_payload.size()) % sizeof(RegisterData) != 0)
  {
    error_str = "register data length error, payload size must divisible by 4, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }

  std::queue<uint32_t>().swap(register_value_queue);
  std::queue<uint32_t>().swap(register_addr_queue);

  RegisterData register_data;
  for (size_t i = 0; i < _payload.size(); i += sizeof(register_data))
  {
    memcpy(&register_data, &_payload.at(i), sizeof(register_data));
    register_addr_queue.emplace(register_data.address);
    register_value_queue.emplace(register_data.value);
  }

  return true;
}

bool MechBaseCodec::extractNormalPacket(const std::vector<uint8_t>& _packet)
{
  if (!_packet.empty())
  {
    universal_queue.emplace(_packet.begin(), _packet.end());
  }
  return true;
}

bool MechBaseCodec::extractMultiRegister(const std::vector<uint8_t>& _payload)
{

  if ((_payload.size()) % sizeof(uint32_t) != 0)
  {
    error_str = "register data length error, payload size must divisible by 4, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }

  if (!register_value_queue.empty())
  {
    std::queue<uint32_t>().swap(register_value_queue);
  }

  uint32_t register_data = 0;
  for (size_t i = 0; i < _payload.size(); i += sizeof(register_data))
  {
    memcpy(&register_data, &_payload.at(i), sizeof(register_data));
    register_value_queue.emplace(register_data);
  }

  return true;
}
bool MechBaseCodec::extractConRegister(const std::vector<uint8_t>& _payload)
{
  if ((_payload.size()) % sizeof(uint32_t) != 0)
  {
    error_str = "register data length error, payload size must divisible by 4, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }
  if (!register_value_queue.empty())
  {
    std::queue<uint32_t>().swap(register_value_queue);
  }
  if (_payload.size() < sizeof(uint32_t) * 2)
  {
    error_str = "register data length error, payload size must be greater than 8, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }

  uint32_t register_data = 0;
  for (size_t i = sizeof(register_data); i < _payload.size(); i += sizeof(register_data))
  {
    memcpy(&register_data, &_payload.at(i), sizeof(register_data));
    register_value_queue.emplace(register_data);
  }
  return true;
}

bool MechBaseCodec::extractConfigRegister(const std::vector<uint8_t>& _payload)
{
  if (_payload.size() < 2)
  {
    is_config_register_read_finish = true;
    return true;
  }

  if (_payload.size() < sizeof(uint16_t) * 3)
  {
    error_str = "config register data length error, payload size must be greater than 4, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }

  uint16_t reg_num            = 0;
  uint16_t curr_frame_num     = 0;
  uint16_t curr_frame_reg_num = 0;

  size_t index = 0;
  MechBaseCodec::copyFromPayload(_payload, reg_num, index);
  MechBaseCodec::copyFromPayload(_payload, curr_frame_num, index);
  MechBaseCodec::copyFromPayload(_payload, curr_frame_reg_num, index);

  if (_payload.size() != static_cast<size_t>(curr_frame_reg_num) * 8 + sizeof(uint16_t) * 3)
  {
    error_str = "config register data length error, payload size must be equal to " + std::to_string(reg_num * 8 + 4) +
                ", payload actual size:" + std::to_string(_payload.size());
    return false;
  }

  for (size_t i = 0; i < curr_frame_reg_num; ++i)
  {
    uint32_t reg_addr = 0;
    uint32_t reg_val  = 0;
    MechBaseCodec::copyFromPayload(_payload, reg_addr, index);
    MechBaseCodec::copyFromPayload(_payload, reg_val, index);
    register_addr_queue.emplace(reg_addr);
    register_value_queue.emplace(reg_val);
  }

  if (register_addr_queue.size() == reg_num)
  {
    is_config_register_read_finish = true;
  }

  return true;
}

bool MechBaseCodec::extractConfigPara(const std::vector<uint8_t>& _payload)
{
  if (_payload.size() != sizeof(mech::ConfigPara))
  {
    return false;
  }
  mech::ConfigPara config_para {};
  std::memcpy(&config_para, _payload.data(), sizeof(mech::ConfigPara));
  universal_queue.emplace(_payload.begin(), _payload.end());
  return true;
}

bool MechBaseCodec::packWriteConfigPara(const helios::ConfigPara& /*_config_paramater*/,
                                        uint32_t& /*_expected_packet_response_code*/,
                                        std::vector<uint8_t>& /*_packet*/)
{
  error_str = "unsupported function: packWriteConfigPara";
  return false;
}

bool MechBaseCodec::extractPSDefined(const std::vector<uint8_t>& _payload)
{
  if (_payload.size() != 5)
  {
    error_str =
      "ps defined data length error, payload size must be 5, payload actual size:" + std::to_string(_payload.size());
    return false;
  }
  universal_queue.emplace(_payload.begin(), _payload.end());
  return true;
}

bool MechBaseCodec::extractData(const std::vector<uint8_t>& _packet)
{
  LOG_INDEX_ERROR("unsupported func");
  return false;
}

bool MechBaseCodec::extractData(const uint32_t _response_code, const std::vector<uint8_t>& _payload)
{
  switch (_response_code)
  {
  case mech::NET_CMD_TOP_BOARD_MULTI_READ_REGISTER:
  case mech::NET_CMD_BOTTOM_BOARD_MULTI_READ_REGISTER:
  {
    return extractMultiRegister(_payload);
  }
  case mech::NET_CMD_TOP_BOARD_CON_READ_REGISTER:
  case mech::NET_CMD_BOTTOM_BOARD_CON_READ_REGISTER:
  {
    return extractConRegister(_payload);
  }
  case mech::NET_CMD_CONFIG_READ_ALL:
  {
    return extractConfigPara(_payload);
  }
  case mech::NET_CMD_PS_DEFINED:
  {
    return extractPSDefined(_payload);
  }

  case mech::NET_CMD_CONFIG_SET_NETWORK:
  case mech::NET_CMD_BOTTOM_BOARD_MULTI_WRITE_REGISTER:
  case mech::NET_CMD_BOTTOM_BOARD_CON_WRITE_REGISTER:
  {
    return true;
  }

  case mech::NET_CMD_OTHER_FIRM_459_READ:
  case mech::NET_CMD_OTHER_FIRM_BOT_READ:
  case mech::NET_CMD_OTHER_ENCOD_CALIB_READ:
  case mech::NET_CMD_OTHER_FIRM_TOP_READ:
  {
    return extractConfigRegister(_payload);
  }

  default:
    error_str = "unimplemented response code: " + MechBaseCodec::hex(_response_code);
    return extractNormalPacket(_payload);
  }
  return true;
}

}  // namespace lidar
}  // namespace robosense
