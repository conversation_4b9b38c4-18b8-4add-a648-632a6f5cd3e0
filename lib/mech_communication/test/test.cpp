/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_communication/protocol/codec/airy_codec.h"
#include "mech_communication/protocol/codec/mech_base_codec.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include <cstdint>
#include <gtest/gtest.h>

#include "mech_communication/mech_communication.h"
#include <iostream>
#include <vector>

#include "rsfsc_log/rsfsc_log.h"

TEST(NopTest, TestOne) {}

template <typename T>
bool compareVectors(const std::vector<T>& _expected, const std::vector<T>& _actual)
{
  if (_expected.size() != _actual.size())
  {
    return false;
  }

  auto mismatch_pair = std::mismatch(_expected.begin(), _expected.end(), _actual.begin());
  if (mismatch_pair.first != _expected.end())
  {
    std::cout << "First mismatch at index " << std::distance(_expected.begin(), mismatch_pair.first) << std::endl;
    std::cout << "Expected: " << static_cast<uint32_t>(*mismatch_pair.first)
              << ", Actual: " << static_cast<uint32_t>(*mismatch_pair.second) << std::endl;
    return false;
  }

  return true;
}

int main(int _argc, char** _ptr_argv)
{
  testing::InitGoogleTest(&_argc, _ptr_argv);
  return RUN_ALL_TESTS();
}
