/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef AIRY_CRC32_CODEC_H
#define AIRY_CRC32_CODEC_H

#include "airy_codec.h"
#include <cstdint>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
class AiryCrc32Codec : public AiryCodec
{
public:
  AiryCrc32Codec();
  AiryCrc32Codec(AiryCrc32Codec&&) noexcept = delete;
  AiryCrc32Codec(const AiryCrc32Codec&)     = delete;
  AiryCrc32Codec& operator=(AiryCrc32Codec&&) = delete;
  AiryCrc32Codec& operator=(const AiryCrc32Codec&) = delete;
  ~AiryCrc32Codec() override                       = default;

  std::string getCurrentProtocolType() override;
  std::vector<uint8_t> frameTailPack(std::vector<uint8_t>& _frame_array) override;
  bool parseClientPacket(std::vector<uint8_t>& _packet_buffer) override;
  bool parseServerPacket(std::vector<uint8_t>& _packet_buffer) override;

  static uint32_t checkSum32(const uint8_t* _data, uint32_t _length);

private:
};

}  // namespace lidar
}  // namespace robosense

#endif  //AIRY_CRC32_CODEC_H
