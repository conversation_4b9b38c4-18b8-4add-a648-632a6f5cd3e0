﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef MECH_BASE_CODEC_H
#define MECH_BASE_CODEC_H

#include <algorithm>
#include <cstdint>
#include <cstring>
#include <map>
#include <memory>
#include <mutex>
#include <queue>
#include <string>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace helios
{
struct ConfigPara;
}  // namespace helios
namespace mech
{
struct IntensityData;

enum class ResponseType;
}  // namespace mech

enum class ProtocolType;
enum class CommClientType;
struct CommunicationInfo;
struct RegisterData
{
  uint32_t address;
  uint32_t value;
  // NOLINTNEXTLINE
  RegisterData(uint32_t _address = 0, uint32_t _value = 0) : address(_address), value(_value) {}
};

struct ExpectedResp
{
  uint32_t cmd;
  std::vector<uint8_t> data;
};

class MechBaseCodec
{
public:
public:
  MechBaseCodec()                         = default;
  MechBaseCodec(MechBaseCodec&&) noexcept = delete;
  MechBaseCodec(const MechBaseCodec&)     = delete;
  MechBaseCodec& operator=(MechBaseCodec&&) = delete;
  MechBaseCodec& operator=(const MechBaseCodec&) = delete;
  virtual ~MechBaseCodec()                       = default;

  std::queue<std::pair<uint32_t, std::vector<uint8_t>>> payload_queue;
  std::queue<uint32_t> register_value_queue;
  std::queue<uint32_t> register_addr_queue;
  std::queue<std::vector<uint8_t>> universal_queue;
  bool is_config_register_read_finish = false;

  [[nodiscard]] int getLogIndex() const { return log_index; }
  void setLogIndex(const int _index) { log_index = _index; }

  void clear();

  virtual bool parseClientPacket(std::vector<uint8_t>& _packet_buffer);
  virtual bool parseServerPacket(std::vector<uint8_t>& _packet_buffer);

  virtual std::vector<uint32_t> handleParsedPayloads();

  virtual uint16_t checkSum(const uint8_t* _data, uint16_t _length);

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  virtual std::vector<uint8_t> requestFrameHeadPack(const uint32_t _cmd_type, const uint32_t _payload_size);
  virtual std::vector<uint8_t> responseFrameHeadPack(const uint32_t _cmd_type,
                                                     const mech::ResponseType _response_type,
                                                     const uint32_t _payload_size);
  virtual std::vector<uint8_t> frameTailPack(std::vector<uint8_t>& _frame_array);
  std::vector<uint8_t> packRequest(const uint32_t _cmd_type, const std::vector<uint8_t>& _payload);
  std::vector<uint8_t> packResponse(const uint32_t _cmd_type,
                                    const mech::ResponseType _response_type,
                                    const std::vector<uint8_t>& _payload);

  static std::string hex(std::vector<uint8_t> _data, uint32_t _display_max_len = 100);
  static std::string hex(uint32_t _data);

  static std::vector<uint8_t>::iterator findFrameFlag(std::vector<uint8_t>& _buffer, uint32_t _frame_flag);

  template <typename T>
  static inline typename std::enable_if<std::is_trivially_copyable<T>::value, void>::type
  copyToPayload(std::vector<uint8_t>& _buffer, const T& _source, size_t& _index)
  {
    // static_assert(std::is_trivially_copyable<T>::value, "Type must be trivially copyable");
    std::memcpy(&_buffer.at(_index), &_source, sizeof(T));
    _index += sizeof(T);
  }
  template <typename T>
  static inline typename std::enable_if<std::is_trivially_copyable<T>::value, void>::type
  copyReverseToPayload(std::vector<uint8_t>& _buffer, const T& _source, size_t& _index)
  {
    static_assert(std::is_trivially_copyable<T>::value, "type must be trivially copyable");

    auto source_ptr = static_cast<const std::array<uint8_t, sizeof(T)>*>(static_cast<const void*>(&_source));
    std::reverse_copy(source_ptr->begin(), source_ptr->end(), _buffer.begin() + static_cast<uint32_t>(_index));

    _index += sizeof(T);
  }

  template <typename T>
  static inline void copyArrayToPayload(std::vector<uint8_t>& _buffer,
                                        const T* _source,
                                        size_t _element_num,
                                        size_t& _index)
  {
    // static_assert(std::is_trivially_copyable<T>::value, "Type must be trivially copyable");
    std::memcpy(&_buffer.at(_index), _source, _element_num * sizeof(T));
    _index += _element_num * sizeof(T);
  }
  template <typename... Args>
  static inline void pushToPayload(std::vector<uint8_t>& _buffer, Args&&... _args)
  {
    size_t size                                 = 0;
    std::array<int, sizeof...(_args) + 1> dummy = { 0, (size += sizeof(_args), 0)... };
    static_cast<void>(dummy);  // Avoid unused variable warning
    size_t index = _buffer.size();
    _buffer.resize(_buffer.size() + size);
    std::array<int, sizeof...(_args) + 1> dummy2 = { 0,
                                                     (copyToPayload(_buffer, std::forward<Args>(_args), index), 0)... };
    static_cast<void>(dummy2);  // Avoid unused variable warning
  }
  template <typename... Args>
  static inline void pushReverseToPayload(std::vector<uint8_t>& _buffer, Args&&... _args)
  {
    size_t size                                 = 0;
    std::array<int, sizeof...(_args) + 1> dummy = { 0, (size += sizeof(_args), 0)... };
    static_cast<void>(dummy);  // Avoid unused variable warning
    size_t index = _buffer.size();
    _buffer.resize(_buffer.size() + size);
    std::array<int, sizeof...(_args) + 1> dummy2 = {
      0, (copyReverseToPayload(_buffer, std::forward<Args>(_args), index), 0)...
    };
    static_cast<void>(dummy2);  // Avoid unused variable warning
  }

  // copyFromPayload
  template <typename T>
  static inline typename std::enable_if<std::is_trivially_copyable<T>::value, void>::type
  copyFromPayload(const std::vector<uint8_t>& _buffer, T& _dest, size_t& _index)
  {
    // static_assert(std::is_trivially_copyable<T>::value, "Type must be trivially copyable");
    std::memcpy(&_dest, &_buffer.at(_index), sizeof(T));
    _index += sizeof(T);
  }

  template <typename T>
  static inline typename std::enable_if<std::is_trivially_copyable<T>::value, void>::type
  copyReverseFromPayload(const std::vector<uint8_t>& _buffer, T& _dest, size_t& _index)
  {
    static_assert(std::is_trivially_copyable<T>::value, "type must be trivially copyable");

    auto dest_ptr = static_cast<std::array<uint8_t, sizeof(T)>*>(static_cast<void*>(&_dest));
    std::reverse_copy(_buffer.begin() + static_cast<uint32_t>(_index),
                      _buffer.begin() + static_cast<uint32_t>(_index) + sizeof(T), dest_ptr->begin());

    _index += sizeof(T);
  }

  template <typename T>
  static inline void copyArrayFromPayload(const std::vector<uint8_t>& _buffer,
                                          T* _dest,
                                          size_t _element_num,
                                          size_t& _index)
  {
    // static_assert(std::is_trivially_copyable<T>::value, "Type must be trivially copyable");
    std::memcpy(_dest, &_buffer.at(_index), _element_num * sizeof(T));
    _index += _element_num * sizeof(T);
  }

  virtual bool packReadRegister(const std::vector<uint32_t>& _reg_addr,
                                uint32_t& _expected_packet_response_code,
                                std::vector<uint8_t>& _packet);
  virtual bool packReadConRegData(const uint32_t _start_reg_addr,
                                  const uint32_t _reg_num,
                                  uint32_t& _expected_packet_response_code,
                                  std::vector<uint8_t>& _packet);
  virtual bool packReadConTopRegData(const uint32_t _start_reg_addr,
                                     const uint32_t _reg_num,
                                     uint32_t& _expected_packet_response_code,
                                     std::vector<uint8_t>& _packet);
  virtual bool packWriteConRegData(const uint32_t _start_reg_addr,
                                   const std::vector<uint32_t>& _reg_val,
                                   uint32_t& _expected_packet_response_code,
                                   std::vector<uint8_t>& _packet);
  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  virtual bool packWriteRegister(const std::vector<uint32_t>& _reg_addr,
                                 const std::vector<uint32_t>& _reg_val,
                                 uint32_t& _expected_packet_response_code,
                                 std::vector<uint8_t>& _packet);
  virtual bool packWriteRegister(const std::vector<RegisterData>& _reg_data_vec,
                                 uint32_t& _expected_packet_response_code,
                                 std::vector<uint8_t>& _packet);

  virtual bool packWriteDigitalRegister(const RegisterData _register_data,
                                        uint32_t& _expected_packet_response_code,
                                        std::vector<uint8_t>& _packet);
  virtual bool packReadDigitalRegister(const uint32_t _reg_addr,
                                       uint32_t& _expected_packet_response_code,
                                       std::vector<uint8_t>& _packet);

  virtual bool packGetEyesSafe(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet);

  virtual bool packSetEyesSafe(const uint32_t _is_open,
                               uint32_t& _expected_packet_response_code,
                               std::vector<uint8_t>& _packet);

  virtual bool packReadConfigPara(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet);

  virtual bool packWriteConfigPara(const helios::ConfigPara& /*_config_paramater*/,
                                   uint32_t& /*_expected_packet_response_code*/,
                                   std::vector<uint8_t>& /*_packet*/);

  virtual bool packReadCmd(const uint32_t _cmd_type,
                           const std::vector<uint8_t>& _data,
                           uint32_t& _expected_packet_response_code,
                           std::vector<uint8_t>& _packet);

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  virtual bool packWriteCmd(const uint32_t _cmd_type,
                            const std::vector<uint8_t>& _data,
                            uint32_t& _expected_packet_response_code,
                            std::vector<uint8_t>& _packet);

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  virtual bool packWriteIpPort(const uint32_t /*_ip*/,
                               uint16_t /*_msop_port*/,
                               uint16_t /*_difop_port*/,
                               uint32_t& /*_expected_packet_response_code*/,
                               std::vector<uint8_t>& /*_packet*/);

  virtual bool packWriteTopRegister(const std::vector<uint32_t>& _reg_addr,
                                    const std::vector<uint32_t>& _reg_val,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet);
  virtual bool packWriteTopRegister(const std::vector<RegisterData>& _reg_addr,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet);
  virtual bool packWriteConTopRegData(const uint32_t _start_reg_addr,
                                      const std::vector<uint32_t>& _reg_val,
                                      uint32_t& _expected_packet_response_code,
                                      std::vector<uint8_t>& _packet);

  virtual bool packReadTopRegister(const std::vector<uint32_t>& _reg_data,
                                   uint32_t& _expected_packet_response_code,
                                   std::vector<uint8_t>& _packet);

  virtual std::string getCurrentProtocolType();

  virtual bool packReadIntensity(const mech::IntensityData& _intensity_data,
                                 uint32_t& _expected_packet_response_code,
                                 std::vector<uint8_t>& _packet);

  virtual bool getTxChannelRegAddr(const int _channel_num, uint32_t& _tx_channel_reg_addr);
  virtual bool packCtrlTxChannelExclusively(const int _channel_num,
                                            const bool _is_open,
                                            uint32_t& _expected_packet_response_code,
                                            std::vector<uint8_t>& _packet);
  virtual bool packReadTxChannelAll(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet);
  virtual bool packCtrlTxChannelAll(const bool _open,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet);
  virtual bool packCtrlTxChannel(const int _channel_num,
                                 const bool _open,
                                 const uint32_t _curr_value,
                                 uint32_t& _expected_packet_response_code,
                                 std::vector<uint8_t>& _packet);

  virtual bool packStartWriteTopFlash(const uint32_t _start_addr,
                                      const uint32_t _len,
                                      ExpectedResp& _expected_resp,
                                      std::vector<uint8_t>& _packet);
  virtual bool packWriteTopFlash(const uint32_t _pkt_count,
                                 const std::vector<uint8_t>& _data,
                                 ExpectedResp& _expected_resp,
                                 std::vector<uint8_t>& _packet);
  virtual bool packFinishWriteTopFlash(ExpectedResp& _expected_resp, std::vector<uint8_t>& _packet);

  virtual bool extractData(const uint32_t _response_code, const std::vector<uint8_t>& _payload);
  virtual bool extractRegister(const std::vector<uint8_t>& _payload);
  virtual bool extractMultiRegister(const std::vector<uint8_t>& _payload);
  virtual bool extractConRegister(const std::vector<uint8_t>& _payload);
  virtual bool extractData(const std::vector<uint8_t>& _packet);
  virtual bool extractNormalPacket(const std::vector<uint8_t>& _packet);
  virtual bool extractConfigRegister(const std::vector<uint8_t>& _payload);
  virtual bool extractConfigPara(const std::vector<uint8_t>& _payload);
  virtual bool extractPSDefined(const std::vector<uint8_t>& _payload);

  virtual bool isValidMsop(const char* _packet);

  std::string error_str = "unkown error";
  int log_index         = -1;
  std::mutex queue_mutex;

private:
};

}  // namespace lidar
}  // namespace robosense

#endif  // MECH_BASE_CODEC_H
